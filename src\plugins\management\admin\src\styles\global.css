/* Global styles for Management Plugin */

/* Plugin wrapper styling */
.management-plugin {
  /* Layout */
  width: 100%;
  min-height: 100vh;
  position: relative;

  /* Background */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  /* Border and shadow */
  border-radius: 12px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.05);

  /* Spacing */
  margin: 0;
  padding: 0;

  /* Overflow */
  overflow: hidden;

  /* Animation */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Plugin wrapper hover effect */
.management-plugin:hover {
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* Font family override */
.management-plugin * {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
}

/* Management Container */
.management-container {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif !important;
}

.management-container * {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif !important;
}

.management-container .ant-layout {
  min-height: 100vh;
  background: #f8fafc;
  gap: 20px;
}

.management-container .ant-layout-sider {
  background: #ffffff;
}

.management-container .ant-menu {
  border-right: none;
  background: transparent;
}

.management-container .ant-menu-item {
  margin: 4px 8px;
  border-radius: 8px;
  height: 48px;
  line-height: 48px;
  display: flex;
  align-items: center;
}

.management-container .ant-menu-item-selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.management-container .ant-menu-item:hover {
  background: #f0f2ff;
  color: #667eea;
}

.management-container .ant-menu-item-selected:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-logo {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sidebar-logo-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-logo-image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.sidebar-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-menu-item {
  margin-bottom: 4px;
}

.sidebar-menu-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0;
}

.sidebar-menu-button:hover {
  background: #f8f9fa;
  color: #333;
}

.sidebar-menu-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-menu-button-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-menu-button-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.sidebar-menu-button-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-menu-button-text {
  opacity: 0;
}

.sidebar-chevron {
  width: 16px;
  height: 16px;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex-shrink: 0;
  transform-origin: center;
}

.sidebar-chevron.rotated {
  transform: rotate(180deg);
}

.sidebar-submenu {
  background: #f8f9fa;
  border-left: 3px solid #e9ecef;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 0 8px 8px 0;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  visibility: hidden;
  transition:
    max-height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    visibility 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(-8px) scale(0.98);
  transform-origin: top;
  /* Sử dụng biến chiều cao động */
  max-height: 0;
}

.sidebar-submenu.expanded {
  max-height: var(--submenu-height, 400px);
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  transition:
    max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    visibility 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-submenu.collapsing {
  max-height: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px) scale(0.98);
  transition:
    max-height 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53),
    opacity 0.25s cubic-bezier(0.55, 0.085, 0.68, 0.53),
    visibility 0.25s cubic-bezier(0.55, 0.085, 0.68, 0.53),
    transform 0.25s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.sidebar-submenu-item {
  width: 100%;
  display: block;
  padding: 10px 16px;
  border: none;
  background: transparent;
  color: #666;
  cursor: pointer;
  text-align: left;
  font-size: 13px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid #e9ecef;
}

.sidebar-submenu-item:last-child {
  border-bottom: none;
}

.sidebar-submenu-item:hover {
  background: #e9ecef;
  color: #333;
  padding-left: 20px;
  transform: translateX(4px);
}

.sidebar-submenu-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
}

.sidebar-submenu-item.active:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  padding-left: 16px;
  transform: translateX(2px);
}

/* Table row styling */
.table-row-light {
  background-color: #ffffff;
}

.table-row-dark {
  background-color: #fafafa;
}

.table-row-light:hover,
.table-row-dark:hover {
  background-color: #f0f2ff !important;
}

/* Card styling */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-card-body {
  padding: 24px;
}

/* Button styling */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Input styling */
.ant-input,
.ant-select-selector {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.ant-input:focus,
.ant-select-selector:focus,
.ant-select-focused .ant-select-selector {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* Tag styling */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
}

/* Progress bar animations */
@keyframes loadingAnimation {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.loading-line {
  animation: loadingAnimation 2s ease-in-out infinite;
}

/* Status colors */
.status-pending {
  color: #faad14;
  background-color: #fff7e6;
  border-color: #ffd591;
}

.status-processing {
  color: #1890ff;
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.status-completed {
  color: #52c41a;
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.status-cancelled {
  color: #ff4d4f;
  background-color: #fff2f0;
  border-color: #ffccc7;
}

/* Responsive design */
@media (max-width: 768px) {
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-table-wrapper {
    overflow-x: auto;
  }
}

/* Loading spinner */
.ant-spin-dot {
  color: #667eea;
}

/* Pagination */
.ant-pagination-item-active {
  border-color: #667eea;
  background-color: #667eea;
}

.ant-pagination-item-active a {
  color: #ffffff;
}

/* Menu styling */
.ant-menu-item-selected {
  background-color: #667eea !important;
  color: #ffffff !important;
}

.ant-menu-item:hover {
  color: #667eea !important;
}

/* Notification styling */
.ant-notification {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Modal styling */
.ant-modal {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;
}

.ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Form styling */
.ant-form-item-label > label {
  font-weight: 500;
  color: #2c3e50;
}

/* Drawer styling */
.ant-drawer-content {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Tooltip styling */
.ant-tooltip {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Select dropdown styling */
.ant-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Date picker styling */
.ant-picker {
  border-radius: 8px;
}

.ant-picker:hover,
.ant-picker-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* Upload styling */
.ant-upload-drag {
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
}

.ant-upload-drag:hover {
  border-color: #667eea;
}

/* Steps styling */
.ant-steps-item-process .ant-steps-item-icon {
  background-color: #667eea;
  border-color: #667eea;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #52c41a;
  border-color: #52c41a;
}

/* Badge styling */
.ant-badge-count {
  background-color: #667eea;
  color: #ffffff;
  box-shadow: 0 0 0 1px #ffffff;
}

/* Switch styling */
.ant-switch-checked {
  background-color: #667eea;
}

/* Slider styling */
.ant-slider-track {
  background-color: #667eea;
}

.ant-slider-handle {
  border-color: #667eea;
}

/* Rate styling */
.ant-rate-star-focused,
.ant-rate-star:hover,
.ant-rate-star-full {
  color: #667eea;
}

/* Timeline styling */
.ant-timeline-item-head {
  background-color: #667eea;
  border-color: #667eea;
}

/* Anchor styling */
.ant-anchor-link-active > .ant-anchor-link-title {
  color: #667eea;
}

/* BackTop styling */
.ant-back-top {
  background-color: #667eea;
  color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
